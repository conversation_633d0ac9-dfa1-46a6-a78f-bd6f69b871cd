<script setup>
import { computed, getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericForm from '@/components/GenericForm.vue'
import ApplicationApi from '@/api/llm/application.js'
import MediaApi from '@/api/media/record.js'
import UtilApi from '@/api/util.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.si.dubhe.model.LinkApplication', ['name', 'description', 'link', 'target'])

const fields = ref([{
  title: '应用图标',
  field: '_icon',
  type: 'file',
  config: {
    accept: 'image/*',
    component: 'gallery',
    label: '',
    max: 1,
    showUploadList: {
      showDownloadIcon: true,
      showPreviewIcon: true,
      showRemoveIcon: true
    },
    upload: file => {
      return new Promise((resolve, reject) => {
        MediaApi.uploadForm(null, [file])
          .then(result => {
            resolve({
              id: result.data[0].id,
              url: MediaApi.preview(result.data[0].id)
            })
          })
          .catch(error => {
            reject(error)
          })
      })
    }
  }
}, {
  title: '应用名称',
  field: 'name',
  type: 'text',
  config: {
    promise: _config.name
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '目标地址',
  field: 'link',
  type: 'textarea',
  config: {
    promise: _config.link
  }
}, {
  title: '打开方式',
  field: 'target',
  type: 'radio',
  config: {
    promise: new Promise(resolve => {
      _config.target.then(data => {
        data.options = [{
          label: '新标签页',
          value: 'SELF'
        }, {
          label: '新窗口',
          value: 'BLANK'
        }]

        resolve(data)
      })
    })
  }
}])

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  title: '保存',
  callback (record) {
    const _record = {
      ...record,
      icon: record._icon
    }

    const _promise = ApplicationApi.saveLink(_record)
    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  title: '删除',
  callback (record) {
    const _promise = ApplicationApi.remove(record.id)
    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const applicationId = computed(() => proxy.$route.query.id)

const form = ref()

onMounted(() => {
  if (applicationId.value) {
    // 编辑模式，加载应用数据
    ApplicationApi.get(applicationId.value, {
      showLoading: false,
      toast: {
        success: false
      }
    })
      .then(result => {
        result.data._icon = typeof result.data.icon === 'string'
          ? {
              id: result.data.icon,
              url: MediaApi.preview(result.data.icon)
            }
          : null

        form.value.setModel(result.data)
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})
</script>

<template>
  <a-card :title="'外链应用'">
    <a-row :gutter="24">
      <a-col :span="8">
        <a-card
          :size="'small'"
          :title="'应用说明'"
        >
          <div class="help-content">
            <p>集成外部应用或服务，通过链接快速访问第三方工具。</p>

            <h5>功能特点：</h5>
            <ul>
              <li>统一应用入口管理</li>
              <li>快速集成外部服务</li>
              <li>支持多种打开方式</li>
              <li>支持自定义应用图标</li>
            </ul>

            <h5>图标上传说明：</h5>
            <ul>
              <li><strong>支持格式</strong>：JPG、PNG、GIF、SVG等图片格式</li>
              <li><strong>建议尺寸</strong>：64x64像素或更高分辨率的正方形图片</li>
              <li><strong>文件大小</strong>：建议不超过2MB</li>
            </ul>

            <h5>打开方式说明：</h5>
            <ul>
              <li><strong>新标签页</strong>：在应用的新标签页中打开链接</li>
              <li><strong>新窗口</strong>：在浏览器的新标签页打开链接</li>
            </ul>
          </div>
        </a-card>
      </a-col>

      <a-col :span="16">
        <GenericForm
          ref="form"
          :layout="{
            showLabel: true,
            labelCol: {
              sm: {
                span: 7
              },
              lg: {
                span: 4
              }
            },
            wrapperCol: {
              sm: {
                span: 17
              },
              lg: {
                span: 20
              }
            }
          }"
          :fields="fields"
          :actions="actions"
        />
      </a-col>
    </a-row>
  </a-card>
</template>

<style lang="less" scoped>
.help-content {
  h5 {
    color: #595959;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }

  p {
    color: #8c8c8c;
    margin-bottom: 12px;
    line-height: 1.5;
  }

  ul {
    color: #8c8c8c;
    font-size: 13px;
    line-height: 1.5;

    li {
      margin-bottom: 4px;

      strong {
        color: #595959;
      }
    }
  }
}
</style>
